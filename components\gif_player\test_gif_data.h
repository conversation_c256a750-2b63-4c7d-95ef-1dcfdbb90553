/**
 * @file test_gif_data.h
 * @brief Generated GIF data array from test.gif
 * 
 * This file was automatically generated by gif_to_c_array.py
 * Do not edit manually.
 */

#ifndef TEST_GIF_DATA_H
#define TEST_GIF_DATA_H

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// GIF data size: 1407 bytes
extern const uint8_t test_gif_data[];
extern const size_t test_gif_data_size;

#ifdef __cplusplus
}
#endif

#endif /* TEST_GIF_DATA_H */
