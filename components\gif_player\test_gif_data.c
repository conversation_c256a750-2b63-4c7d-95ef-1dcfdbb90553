/**
 * @file test_gif_data.c
 * @brief Generated GIF data array from test.gif
 * 
 * This file was automatically generated by gif_to_c_array.py
 * Do not edit manually.
 */

#include "test_gif_data.h"

// GIF data array (509 bytes)
const uint8_t test_gif_data[] = {
    0x47, 0x49, 0x46, 0x38, 0x39, 0x61, 0x14, 0x00, 0x14, 0x00, 0xb3, 0x0e, 0x00, 0xd1, 0xd1, 0xd1, 
    0xff, 0xb6, 0xb6, 0x34, 0x99, 0x37, 0x35, 0x99, 0x39, 0x36, 0xa8, 0x3a, 0xea, 0xea, 0xea, 0x47, 
    0xaa, 0x4a, 0xac, 0xac, 0xac, 0x2a, 0x8c, 0x2d, 0x99, 0x99, 0x99, 0xcc, 0xcc, 0xcc, 0x66, 0x66, 
    0x66, 0x33, 0x33, 0x33, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x21, 0xff, 0x0b, 
    0x4e, 0x45, 0x54, 0x53, 0x43, 0x41, 0x50, 0x45, 0x32, 0x2e, 0x30, 0x03, 0x01, 0x00, 0x00, 0x00, 
    0x21, 0xf9, 0x04, 0x05, 0x32, 0x00, 0x0e, 0x00, 0x2c, 0x00, 0x00, 0x00, 0x00, 0x14, 0x00, 0x14, 
    0x00, 0x00, 0x04, 0x85, 0xd0, 0xc9, 0x49, 0xab, 0xa5, 0xe9, 0xca, 0x7c, 0x93, 0x5a, 0x9d, 0xc2, 
    0x58, 0x49, 0xf3, 0x75, 0xe6, 0x88, 0x35, 0x69, 0x55, 0xb6, 0xd3, 0xc2, 0xa6, 0x4c, 0x5d, 0xcb, 
    0xac, 0x18, 0xcf, 0x7c, 0x2f, 0xeb, 0x0e, 0x5c, 0x6f, 0xd8, 0xf8, 0x8d, 0x84, 0xc4, 0xa1, 0x0e, 
    0xc7, 0xc0, 0x21, 0x1b, 0xcd, 0xdc, 0xb1, 0x11, 0xa8, 0x15, 0x4d, 0x50, 0x56, 0x2d, 0xd0, 0x62, 
    0x50, 0x03, 0x80, 0xf0, 0x81, 0x11, 0x06, 0x1c, 0x0e, 0x01, 0x2e, 0xd0, 0x6b, 0x30, 0x20, 0x10, 
    0xe7, 0x43, 0xfb, 0x2d, 0xa5, 0x30, 0x0c, 0x04, 0xc2, 0xfb, 0x8d, 0xd7, 0x8b, 0x54, 0x14, 0x03, 
    0x02, 0x02, 0x08, 0x73, 0x82, 0x0b, 0x88, 0x80, 0x14, 0x08, 0x84, 0x85, 0x8d, 0x02, 0x32, 0x8a, 
    0x8b, 0x7b, 0x03, 0x03, 0x7b, 0x36, 0x1a, 0x13, 0x0c, 0x7b, 0x0a, 0x9d, 0x40, 0x99, 0x0b, 0x7f, 
    0x37, 0x9f, 0x1a, 0x0b, 0x35, 0x31, 0xa7, 0x13, 0x11, 0x00, 0x21, 0xf9, 0x04, 0x05, 0x32, 0x00, 
    0x0e, 0x00, 0x2c, 0x04, 0x00, 0x09, 0x00, 0x0c, 0x00, 0x01, 0x00, 0x00, 0x04, 0x05, 0xb0, 0xc9, 
    0xb6, 0xa6, 0x8d, 0x00, 0x21, 0xf9, 0x04, 0x09, 0x32, 0x00, 0x0e, 0x00, 0x2c, 0x01, 0x00, 0x01, 
    0x00, 0x13, 0x00, 0x09, 0x00, 0x00, 0x04, 0x11, 0xd0, 0xc9, 0x49, 0xab, 0xbd, 0x38, 0xeb, 0xcd, 
    0xbb, 0xff, 0x60, 0x28, 0x31, 0x0b, 0x46, 0x52, 0x11, 0x00, 0x21, 0xf9, 0x04, 0x05, 0x14, 0x00, 
    0x0e, 0x00, 0x2c, 0x00, 0x00, 0x00, 0x00, 0x14, 0x00, 0x14, 0x00, 0x00, 0x04, 0x7f, 0xd0, 0xc9, 
    0x49, 0xab, 0xbd, 0x38, 0x27, 0x8d, 0x93, 0x5a, 0x97, 0xc7, 0x58, 0x49, 0xf3, 0x85, 0xe6, 0x48, 
    0x95, 0x69, 0xc5, 0x2a, 0xaa, 0xb4, 0x34, 0x34, 0xcc, 0xdc, 0xf7, 0x5c, 0xc7, 0x3a, 0xed, 0xff, 
    0x8d, 0x19, 0x4c, 0x06, 0x2c, 0xfa, 0x84, 0xa3, 0x5e, 0x03, 0x70, 0x30, 0xfa, 0x86, 0xca, 0x02, 
    0xd3, 0xd9, 0x9a, 0x05, 0x6e, 0x87, 0x6c, 0x93, 0x41, 0xbb, 0x05, 0x5a, 0xdc, 0x40, 0x00, 0x40, 
    0x3e, 0x30, 0xc8, 0xcc, 0x83, 0xb8, 0xe5, 0xe0, 0x1a, 0x0c, 0x08, 0x84, 0xf6, 0x1d, 0xdf, 0x51, 
    0x18, 0x06, 0x02, 0x21, 0x1e, 0xcf, 0xef, 0x6d, 0x16, 0x03, 0x02, 0x02, 0x08, 0x74, 0x82, 0x0b, 
    0x88, 0x31, 0x14, 0x08, 0x84, 0x85, 0x8d, 0x02, 0x33, 0x8a, 0x8b, 0x7c, 0x03, 0x03, 0x7c, 0x38, 
    0x19, 0x32, 0x97, 0x0c, 0x0b, 0x43, 0x99, 0x0b, 0x37, 0x13, 0xa0, 0x8a, 0x11, 0x00, 0x21, 0xf9, 
    0x04, 0x05, 0x14, 0x00, 0x0e, 0x00, 0x2c, 0x06, 0x00, 0x08, 0x00, 0x04, 0x00, 0x05, 0x00, 0x00, 
    0x04, 0x0a, 0xd0, 0xc9, 0xd3, 0x9a, 0xa3, 0x6d, 0xd1, 0x65, 0xe5, 0x8d, 0x00, 0x21, 0xf9, 0x04, 
    0x05, 0x14, 0x00, 0x0e, 0x00, 0x2c, 0x08, 0x00, 0x08, 0x00, 0x05, 0x00, 0x04, 0x00, 0x00, 0x04, 
    0x0a, 0xb0, 0xc9, 0x29, 0x01, 0x58, 0xe0, 0xe8, 0xac, 0x4f, 0x04, 0x00, 0x3b

};

const size_t test_gif_data_size = sizeof(test_gif_data);
