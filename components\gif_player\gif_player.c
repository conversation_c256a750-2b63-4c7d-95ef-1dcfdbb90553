/**
 * @file gif_player.c
 * @brief ESP-IDF component for playing animated GIFs using LVGL
 */

#include "gif_player.h"
#include "esp_log.h"
#include "esp_heap_caps.h"
#include <string.h>
#include <stdio.h>

static const char *TAG = "gif_player";

/**
 * @brief Internal GIF player structure
 */
typedef struct gif_player_s {
    lv_obj_t *gif_obj;              /*!< LVGL GIF object */
    char *file_path;                /*!< Copy of file path */
    bool is_playing;                /*!< Playback state */
    bool loop_forever;              /*!< Loop forever flag */
    lv_timer_t *restart_timer;      /*!< Timer for automatic restart */
} gif_player_t;

static bool fs_initialized = false;

/**
 * @brief Event callback for GIF ready event (end of animation)
 */
static void gif_ready_event_cb(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *gif_obj = lv_event_get_target(e);
    
    if (code == LV_EVENT_READY) {
        // Get the gif_player handle from user data
        gif_player_t *player = (gif_player_t *)lv_event_get_user_data(e);
        
        if (player && player->loop_forever) {
            ESP_LOGD(TAG, "GIF animation finished, restarting...");
            lv_gif_restart(gif_obj);
        }
    }
}

gif_player_err_t gif_player_init(void)
{
    if (fs_initialized) {
        return GIF_PLAYER_OK;
    }

    // LVGL STDIO file system is automatically initialized when LV_USE_FS_STDIO is enabled
    // We just need to verify it's available
    lv_fs_drv_t *drv = lv_fs_get_drv('S');
    if (drv == NULL) {
        ESP_LOGE(TAG, "STDIO file system driver not found. Make sure LV_USE_FS_STDIO is enabled");
        return GIF_PLAYER_ERR_LVGL_NOT_INIT;
    }

    fs_initialized = true;
    ESP_LOGI(TAG, "GIF player initialized with STDIO file system");

    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_create(const gif_player_config_t *config, gif_player_handle_t *handle)
{
    if (!config || !handle || !config->file_path || !config->parent) {
        ESP_LOGE(TAG, "Invalid arguments");
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    if (!fs_initialized) {
        ESP_LOGE(TAG, "GIF player not initialized. Call gif_player_init() first");
        return GIF_PLAYER_ERR_LVGL_NOT_INIT;
    }

    // Allocate memory for the player structure
    gif_player_t *player = (gif_player_t *)heap_caps_malloc(sizeof(gif_player_t), MALLOC_CAP_8BIT);
    if (!player) {
        ESP_LOGE(TAG, "Failed to allocate memory for GIF player");
        return GIF_PLAYER_ERR_MEMORY;
    }

    // Initialize the structure
    memset(player, 0, sizeof(gif_player_t));
    
    // Copy the file path
    size_t path_len = strlen(config->file_path) + 1;
    player->file_path = (char *)heap_caps_malloc(path_len, MALLOC_CAP_8BIT);
    if (!player->file_path) {
        ESP_LOGE(TAG, "Failed to allocate memory for file path");
        free(player);
        return GIF_PLAYER_ERR_MEMORY;
    }
    strcpy(player->file_path, config->file_path);
    
    player->loop_forever = config->loop_forever;

    // Create the LVGL GIF object
    player->gif_obj = lv_gif_create(config->parent);
    if (!player->gif_obj) {
        ESP_LOGE(TAG, "Failed to create LVGL GIF object");
        free(player->file_path);
        free(player);
        return GIF_PLAYER_ERR_MEMORY;
    }

    // Set position
    lv_obj_set_pos(player->gif_obj, config->x, config->y);

    // Add event callback for GIF ready event
    lv_obj_add_event_cb(player->gif_obj, gif_ready_event_cb, LV_EVENT_READY, player);

    // Prepare the file path with drive letter prefix
    char full_path[256];
    snprintf(full_path, sizeof(full_path), "S:%s", config->file_path);

    // Set the GIF source
    lv_gif_set_src(player->gif_obj, full_path);

    // Check if the GIF was loaded successfully
    // We can't directly check the internal state, but we can assume success if no crash occurred
    ESP_LOGI(TAG, "GIF player created for file: %s", config->file_path);

    player->is_playing = config->auto_start;

    *handle = player;
    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_start(gif_player_handle_t handle)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;
    
    if (!player->is_playing) {
        lv_gif_restart(player->gif_obj);
        player->is_playing = true;
        ESP_LOGD(TAG, "GIF playback started");
    }

    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_stop(gif_player_handle_t handle)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;
    player->is_playing = false;
    
    // Note: LVGL GIF doesn't have a direct stop function, 
    // but we can track the state for our own logic
    ESP_LOGD(TAG, "GIF playback stopped");

    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_restart(gif_player_handle_t handle)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;
    lv_gif_restart(player->gif_obj);
    player->is_playing = true;
    
    ESP_LOGD(TAG, "GIF playback restarted");

    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_set_pos(gif_player_handle_t handle, lv_coord_t x, lv_coord_t y)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;
    lv_obj_set_pos(player->gif_obj, x, y);

    return GIF_PLAYER_OK;
}

lv_obj_t *gif_player_get_obj(gif_player_handle_t handle)
{
    if (!handle) {
        return NULL;
    }

    gif_player_t *player = (gif_player_t *)handle;
    return player->gif_obj;
}

gif_player_err_t gif_player_destroy(gif_player_handle_t handle)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;

    // Clean up LVGL object
    if (player->gif_obj) {
        lv_obj_del(player->gif_obj);
    }

    // Free allocated memory
    if (player->file_path) {
        free(player->file_path);
    }

    free(player);

    ESP_LOGD(TAG, "GIF player destroyed");

    return GIF_PLAYER_OK;
}
